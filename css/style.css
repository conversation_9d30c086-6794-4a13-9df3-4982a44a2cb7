/*
====================================================================
EVENTGARDE - MODERN EVENT MANAGEMENT WEBSITE STYLESHEET
====================================================================

This stylesheet contains all the styling for the EventGarde website,
a premier event management company. The CSS is organized into logical
sections for easy maintenance and updates.

SECTIONS:
1. Reset and Base Styles
2. Typography
3. Header and Navigation
4. Hero Section with Slideshow
5. Layout Components (Grid, Cards, Sections)
6. Service Cards
7. Client Logos
8. Forms and Buttons
9. Footer
10. Responsive Design

Author: EventGarde Development Team
Last Updated: 2025
====================================================================
*/

/*
====================================================================
1. RESET AND BASE STYLES
====================================================================
Global reset and fundamental styling for consistent cross-browser appearance
*/
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/*
====================================================================
2. TYPOGRAPHY
====================================================================
Font definitions, heading styles, and text formatting
*/
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
}

h1 {
    font-size: 3rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 2rem;
}

p {
    margin-bottom: 1rem;
    color: #666;
}

/*
====================================================================
3. HEADER AND NAVIGATION
====================================================================
Fixed header with logo and navigation menu
Includes hover effects and responsive behavior
*/
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo img {
    height: 40px;
    width: auto;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #f39c12;
}

/*
====================================================================
4. HERO SECTION WITH SLIDESHOW
====================================================================
Full-screen hero section featuring:
- Continuous slideshow animation with 5 background images
- Overlay gradient for text readability
- Centered content with call-to-action
- Smooth transitions and animations
*/
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 80px;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5));
    z-index: 1;
}

.hero-slideshow {
    position: absolute;
    top: 0;
    left: 0;
    width: 500%;
    height: 100%;
    z-index: 0;
    display: flex;
    animation: continuousSlide 20s infinite;
}

.hero-slide {
    position: relative;
    width: 20%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    flex-shrink: 0;
}

.hero-slide:nth-child(1) {
    background-image: url('../images/hero-bg1.jpg');
}

.hero-slide:nth-child(2) {
    background-image: url('../images/hero-bg2.jpg');
}

.hero-slide:nth-child(3) {
    background-image: url('../images/hero-bg3.jpg');
}

.hero-slide:nth-child(4) {
    background-image: url('../images/hero-bg4.jpg');
}

.hero-slide:nth-child(5) {
    background-image: url('../images/hero-bg1.jpg');
}

@keyframes continuousSlide {
    0% { transform: translateX(0%); }
    18% { transform: translateX(0%); }
    20% { transform: translateX(-20%); }
    38% { transform: translateX(-20%); }
    40% { transform: translateX(-40%); }
    58% { transform: translateX(-40%); }
    60% { transform: translateX(-60%); }
    78% { transform: translateX(-60%); }
    80% { transform: translateX(-80%); }
    100% { transform: translateX(-80%); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 3.5rem;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #f8f9fa;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section Styles */
.section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    position: relative;
    display: inline-block;
}

.section-title h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Card Styles */
.card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Placeholder for missing images */
.card img::before {
    content: "📷 Image Placeholder";
    color: #999;
    font-size: 1rem;
}

/* Services Section */
.services {
    background: #f8f9fa;
}

.service-card {
    text-align: center;
}

.service-card .icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.about-image img {
    width: 100%;
    border-radius: 10px;
}

/* Stats Section */
.stats {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item h3 {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: white;
    font-size: 1.1rem;
}

/* Clients Section */
.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    align-items: center;
}

.client-logo {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.client-logo:hover {
    transform: scale(1.05);
}

.client-logo img {
    max-width: 100%;
    height: 60px;
    object-fit: contain;
}

/* Contact Form */
.contact-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c3e50;
}

/*
====================================================================
UTILITY CLASSES
====================================================================
Reusable utility classes for common styling patterns
*/

.text-center {
    text-align: center;
}

.mt-2 {
    margin-top: 2rem;
}

.cta-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

.cta-section .container {
    text-align: center;
}

.cta-section h2 {
    color: white;
    margin-bottom: 1rem;
}

.cta-section p {
    color: #f8f9fa;
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.logo img {
    height: 40px;
}

.page-hero {
    height: 60vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.page-hero.about-hero {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.page-hero.services-hero {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('../images/hero-bg-services.jpg');
}

.page-hero.clients-hero {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('../images/hero-bg-clients.jpg');
}

.page-hero.contact-hero {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('../images/hero-bg-contact.jpg');
}

.bg-light {
    background: #f8f9fa;
}

/* Clickable email card styling */
a.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

a.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    cursor: pointer;
}

/* Email links within cards */
.card a {
    transition: color 0.2s ease;
}

.card a:hover {
    color: #f39c12 !important;
    text-decoration: underline !important;
}

/* Footer email links */
.footer-section a {
    transition: color 0.2s ease;
}

.footer-section a:hover {
    color: #f39c12 !important;
    text-decoration: underline !important;
}

/*
====================================================================
9. FOOTER
====================================================================
Site footer with company information and contact details
*/
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1.5fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
    }
}

.footer-section h3 {
    color: #e74c3c;
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
}

.footer-section a:hover {
    color: #e74c3c;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #95a5a6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .about-content {
        grid-template-columns: 1fr;
    }
    
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .section {
        padding: 60px 0;
    }
}
