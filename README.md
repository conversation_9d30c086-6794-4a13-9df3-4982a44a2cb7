# EventGarde - Premier Event Management Website

![EventGarde Logo](images/EG%20White%20logo%20Transparent.png)

A modern, professional website for EventGarde, a premier event management company with over 15 years of experience. Built with clean HTML and CSS for optimal performance and maintainability.

## Company Overview

**EventGarde** is the leading events management company established in Egypt by **<PERSON><PERSON> - Founder & CEO**. With wings spread across major cities and reaching Saudi Arabia & United Arab Emirates.

### Key Statistics
- **Experience**: 15+ years in event management
- **Events Managed**: 120+ high-end and ultra-elite events
- **Business Budget**: ~116 million EGP
- **Locations**: Cairo (Egypt), Dubai (UAE), Saudi Arabia

## Website Features

- **Responsive Design** - Optimized for all devices and screen sizes
- **Hero Slideshow** - Animated background slideshow with 5 rotating images
- **Modern Layout** - CSS Grid and Flexbox for professional presentation
- **Service Showcase** - Comprehensive display of event management services
- **Client Portfolio** - Extensive gallery showcasing trusted brand partnerships
- **Contact Integration** - Multiple contact methods for Cairo and Dubai offices
- **SEO Optimized** - Semantic HTML structure with proper meta tags
- **Performance Focused** - Clean code with comprehensive documentation

## Website Pages

1. **Homepage (index.html)**
   - Hero slideshow with company introduction
   - About preview section
   - Services overview with 5 main categories
   - Client logos showcase
   - Call-to-action section

2. **About Us (about.html)**
   - Company story and background
   - Mission and vision statements
   - Core values (6 key principles)
   - Why choose EventGarde section

3. **Services (services.html)**
   - Corporate Events management
   - Social Events planning
   - Exhibitions & Trade Shows
   - Entertainment Events
   - Event Consulting services

4. **Clients (clients.html)**
   - Comprehensive client logo gallery
   - Case studies and portfolio
   - Success stories and testimonials

5. **Contact (contact.html)**
   - Contact form for inquiries
   - Cairo and Dubai office information
   - Multiple contact methods
   - Business hours and locations

## File Structure

```
Eventgarde_v02/
├── README.md                           # Project documentation
├── index.html                          # Homepage
├── about.html                          # About us page
├── services.html                       # Services overview
├── clients.html                        # Client portfolio
├── contact.html                        # Contact information
├── css/
│   └── style.css                      # Main stylesheet (fully documented)
└── images/                            # Image assets
    ├── EG White logo Transparent.png  # Company logo
    ├── EventGarde Portofolio.pdf     # Company portfolio document
    ├── hero-bg*.jpg                  # Hero section backgrounds (4 images)
    ├── about-*.jpg                   # About page images
    ├── case-study-*.jpg              # Portfolio case studies (17 images)
    ├── client-*.jpg                  # Client logos (22 images)
    ├── corporate-events.jpg          # Service category images
    ├── social-events.jpg
    ├── exhibitions.jpg
    ├── entertainment.jpg
    └── consulting.jpg
```

## Design Features

### Color Palette
- **Primary Gradient**: Purple to Blue (#667eea to #764ba2)
- **Accent Color**: Orange (#f39c12 to #e67e22)
- **Text Colors**: Dark gray (#333) for headings, medium gray (#666) for body text
- **Background**: White with light gray (#f8f9fa) sections

### Typography
- **Font Family**: Arial, sans-serif (web-safe fonts)
- **Heading Hierarchy**: Clear distinction between h1, h2, h3 elements
- **Responsive Text**: Scales appropriately on different screen sizes

### Layout Components
- **Header**: Fixed navigation with gradient background
- **Hero Sections**: Full-height sections with centered content
- **Card Components**: Elevated cards with hover effects
- **Grid Layouts**: Responsive CSS Grid for various content arrangements
- **Footer**: Multi-column footer with contact information and links

## Responsive Breakpoints

- **Desktop**: 1200px+ (full layout)
- **Tablet**: 768px-1199px (adjusted grid columns)
- **Mobile**: Below 768px (single column, stacked navigation)

## Browser Compatibility

This website uses modern CSS features and is compatible with:
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Adding Images

The website currently uses placeholder references for images. To add real images:

1. Add image files to the `images/` folder
2. Use the following naming convention and dimensions:
   - `hero-bg.jpg` (1920x1080) - Hero background
   - `about-preview.jpg` (600x400) - About section preview
   - `team-1.jpg`, `team-2.jpg`, `team-3.jpg` (300x300) - Team photos
   - `client-1.jpg` through `client-12.jpg` (200x100) - Client logos
   - Service images (400x300) - Various service illustrations
   - Case study images (400x300) - Project showcases

## Customization

### Changing Colors
Edit the CSS custom properties in `css/style.css`:
- Update gradient colors in `.header`, `.hero`, and `.stats` sections
- Modify accent colors in `.btn` and `.section-title h2::after`

### Updating Content
- Replace placeholder text in HTML files
- Update contact information in footer sections
- Modify service offerings and descriptions as needed

### Adding New Sections
Follow the existing pattern:
```html
<section class="section">
    <div class="container">
        <div class="section-title">
            <h2>Section Title</h2>
            <p>Section description</p>
        </div>
        <!-- Section content -->
    </div>
</section>
```

## Performance Optimization

- **CSS**: Single stylesheet for all pages
- **Images**: Optimize images for web (compress and use appropriate formats)
- **HTML**: Semantic structure for better SEO
- **No External Dependencies**: Fast loading with no external libraries

## Future Enhancements

Consider adding:
- Contact form functionality (requires backend)
- Image gallery/lightbox for portfolio
- Blog section for event tips and news
- Client testimonial carousel
- Google Maps integration for office location
- Social media feed integration

## License

This project is created for Eventgarde event management company. All rights reserved.
